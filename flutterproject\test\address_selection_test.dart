import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:zhongmi/order/purchase_page/purchase_order_create.dart';
import 'package:zhongmi/me/addr_lib/address_book_page.dart';

void main() {
  group('地址选择功能测试', () {
    testWidgets('测试地址库按钮点击', (WidgetTester tester) async {
      // 构建购买订单页面
      await tester.pumpWidget(
        MaterialApp(
          home: PurchaseOrderPage(),
        ),
      );

      // 查找地址库按钮
      final addressBookButton = find.text('地址库');
      expect(addressBookButton, findsOneWidget);

      // 点击地址库按钮
      await tester.tap(addressBookButton);
      await tester.pumpAndSettle();

      // 验证是否导航到地址选择页面
      expect(find.text('选择地址'), findsOneWidget);
    });

    testWidgets('测试地址选择模式', (WidgetTester tester) async {
      // 构建地址库页面（选择模式）
      await tester.pumpWidget(
        MaterialApp(
          home: AddressBookPage(isSelectionMode: true),
        ),
      );

      // 验证页面标题为"选择地址"
      expect(find.text('选择地址'), findsOneWidget);

      // 验证不显示新增地址按钮
      expect(find.text('新增地址'), findsNothing);

      // 验证地址项显示箭头图标
      expect(find.byIcon(Icons.arrow_forward_ios), findsWidgets);
    });

    testWidgets('测试地址库普通模式', (WidgetTester tester) async {
      // 构建地址库页面（普通模式）
      await tester.pumpWidget(
        MaterialApp(
          home: AddressBookPage(isSelectionMode: false),
        ),
      );

      // 验证页面标题为"地址库"
      expect(find.text('地址库'), findsOneWidget);

      // 验证显示新增地址按钮
      expect(find.text('新增地址'), findsOneWidget);

      // 验证地址项显示编辑图标
      expect(find.byIcon(Icons.edit_outlined), findsWidgets);
    });

    testWidgets('测试默认地址显示', (WidgetTester tester) async {
      // 构建购买订单页面
      await tester.pumpWidget(
        MaterialApp(
          home: PurchaseOrderPage(),
        ),
      );

      // 等待页面加载完成
      await tester.pumpAndSettle();

      // 验证默认地址是否显示
      expect(find.textContaining('凤凰家园9号栋803'), findsOneWidget);
      expect(find.textContaining('张三 17345362345'), findsOneWidget);
    });
  });

  group('AddressItem 数据模型测试', () {
    test('测试 AddressItem 创建', () {
      final address = AddressItem(
        name: '测试地址',
        phone: '测试联系人 12345678901',
        isDefault: true,
      );

      expect(address.name, '测试地址');
      expect(address.phone, '测试联系人 12345678901');
      expect(address.isDefault, true);
    });
  });
}
