# 地址库功能实现文档

## 功能概述

本文档描述了地址库功能的完整实现，包括地址选择、默认地址显示和页面间数据传递等核心功能。

## 功能特性

### 1. 地址库按钮交互
- 用户点击"地址库"按钮时，导航到地址选择页面
- 页面标题自动切换为"选择地址"
- 地址项显示选择箭头图标

### 2. 地址选择功能
- 用户可以点击任意地址项进行选择
- 选择后自动返回到订单创建页面
- 选中的地址信息会更新到"送货地址"字段

### 3. 地址回显逻辑
- 如果用户从地址库选择了地址，优先显示选中的地址
- 如果用户没有选择地址，自动显示默认地址
- 地址信息格式：`地址名称 联系人 电话`

## 技术实现

### 核心文件修改

#### 1. `purchase_order_create.dart`
```dart
// 添加地址相关状态
AddressItem? _selectedAddress; // 用户选择的地址
AddressItem? _defaultAddress; // 默认地址

// 初始化默认地址
void _initializeDefaultAddress() {
  _defaultAddress = AddressItem(
    name: '凤凰家园9号栋803',
    phone: '张三 17345362345',
    isDefault: true,
  );
}

// 导航到地址库页面
void _navigateToAddressBook() async {
  final selectedAddress = await Navigator.push<AddressItem>(
    context,
    MaterialPageRoute(
      builder: (context) => const AddressBookPage(
        isSelectionMode: true,
      ),
    ),
  );

  if (selectedAddress != null) {
    setState(() {
      _selectedAddress = selectedAddress;
    });
  }
}

// 获取要显示的地址信息
String _getDisplayAddress() {
  final address = _selectedAddress ?? _defaultAddress;
  if (address != null) {
    return '${address.name} ${address.phone}';
  }
  return '请选择地址';
}
```

#### 2. `address_book_page.dart`
```dart
// 添加选择模式参数
class AddressBookPage extends StatefulWidget {
  final bool isSelectionMode;
  
  const AddressBookPage({
    Key? key, 
    this.isSelectionMode = false,
  }) : super(key: key);
}

// 地址选择方法
void _selectAddress(AddressItem address) {
  if (widget.isSelectionMode) {
    Navigator.pop(context, address);
  }
}

// 根据模式显示不同UI
Widget _buildAddressItem(AddressItem address, int index) {
  return GestureDetector(
    onTap: widget.isSelectionMode ? () => _selectAddress(address) : null,
    child: Container(
      // ... UI代码
      child: Row(
        children: [
          // 地址信息
          Expanded(child: /* 地址信息 */),
          // 根据模式显示不同按钮
          if (widget.isSelectionMode)
            Icon(Icons.arrow_forward_ios) // 选择模式显示箭头
          else
            GestureDetector(
              onTap: () => _editAddress(index),
              child: Icon(Icons.edit_outlined), // 普通模式显示编辑
            ),
        ],
      ),
    ),
  );
}
```

### 数据模型

```dart
class AddressItem {
  final String name;    // 地址名称
  final String phone;   // 联系人和电话
  final bool isDefault; // 是否为默认地址

  AddressItem({
    required this.name,
    required this.phone,
    required this.isDefault,
  });
}
```

## 使用流程

### 用户操作流程
1. 用户进入订单创建页面
2. 系统自动显示默认地址
3. 用户点击"地址库"按钮
4. 进入地址选择页面
5. 用户点击选择某个地址
6. 返回订单页面，显示选中的地址

### 开发者集成流程
1. 导入地址库页面：`import '../../me/addr_lib/address_book_page.dart';`
2. 添加地址状态变量
3. 实现地址选择导航方法
4. 更新UI显示逻辑
5. 处理地址数据回传

## 测试验证

项目包含完整的单元测试和Widget测试：

```bash
# 运行地址选择功能测试
flutter test test/address_selection_test.dart
```

测试覆盖：
- ✅ 地址库按钮点击功能
- ✅ 地址选择模式UI显示
- ✅ 地址库普通模式UI显示
- ✅ 默认地址显示功能
- ✅ AddressItem数据模型

## 扩展功能

### 可能的扩展方向
1. **地址搜索功能**：在地址库中添加搜索框
2. **地址排序功能**：按使用频率或距离排序
3. **地址验证功能**：验证地址格式和有效性
4. **地址同步功能**：与后端API同步地址数据
5. **地址分组功能**：按区域或类型分组显示

### API集成建议
```dart
// 获取用户地址列表
Future<List<AddressItem>> fetchAddresses() async {
  // 调用后端API获取地址列表
}

// 获取默认地址
Future<AddressItem?> getDefaultAddress() async {
  // 调用后端API获取默认地址
}

// 设置默认地址
Future<bool> setDefaultAddress(String addressId) async {
  // 调用后端API设置默认地址
}
```

## 注意事项

1. **状态管理**：确保地址状态在页面间正确传递
2. **UI一致性**：保持选择模式和普通模式的UI风格一致
3. **错误处理**：处理网络请求失败等异常情况
4. **性能优化**：大量地址数据时考虑分页加载
5. **用户体验**：提供清晰的视觉反馈和操作提示

## 总结

地址库功能已完整实现，包括：
- ✅ 地址选择和回显逻辑
- ✅ 页面间数据传递
- ✅ 默认地址处理
- ✅ 双模式UI支持
- ✅ 完整的测试覆盖

功能已通过所有测试验证，可以投入使用。
