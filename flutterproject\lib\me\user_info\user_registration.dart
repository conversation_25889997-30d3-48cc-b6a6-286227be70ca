import 'package:flutter/material.dart';
import 'user_verifycode_login.dart';

class RegisterPage extends StatefulWidget {
  const RegisterPage({super.key});

  @override
  State<RegisterPage> createState() => _RegisterPageState();
}

class _RegisterPageState extends State<RegisterPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  // 注册页面控制器
  final TextEditingController _registerPhoneController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _confirmPasswordController = TextEditingController();
  final TextEditingController _registerVerificationCodeController = TextEditingController();

  bool _isAgreed = true;
  String _selectedCountryCode = '+853';

  // 国家代码选项
  final List<String> _countryCodes = ['+86', '+853', '+852'];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this, initialIndex: 1);
    _registerPhoneController.text = '66129907';
  }

  @override
  void dispose() {
    _tabController.dispose();
    _registerPhoneController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _registerVerificationCodeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color.fromARGB(255, 255, 255, 255),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.only(top: 80),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 头像和标签页容器
              Container(
                width: double.infinity,
                decoration: const BoxDecoration(
                  color: Color.fromARGB(255, 255, 255, 255),
                  borderRadius: BorderRadius.only(
                    bottomLeft: Radius.circular(33.0),
                    bottomRight: Radius.circular(33.0),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Color.fromARGB(50, 0, 0, 0),
                      offset: Offset(0, 4),
                      blurRadius: 6,
                      spreadRadius: -4,
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    // 头像区域
                    Container(
                      width: 120,
                      height: 120,
                      decoration: const BoxDecoration(
                        color: Color(0xFFE5E5E5),
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(height: 35),
                    // 标签页
                    _buildTabBar(),
                  ],
                ),
              ),
              // TabBarView 内容
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    _buildLoginView(),
                    _buildRegisterView(),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      width: 220,
      height: 45,
      decoration: BoxDecoration(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(0),
      ),
      child: TabBar(
        controller: _tabController,
        indicator: const UnderlineTabIndicator(
          borderSide: BorderSide(
            color: Color(0xFF9C7FD9),
            width: 2.5,
          ),
          insets: EdgeInsets.symmetric(horizontal: 75),
        ),
        labelColor: const Color(0xFF333333),
        unselectedLabelColor: const Color(0xFF999999),
        labelStyle: const TextStyle(
          fontSize: 17,
          fontWeight: FontWeight.w600,
        ),
        unselectedLabelStyle: const TextStyle(
          fontSize: 17,
          fontWeight: FontWeight.w400,
        ),
        dividerColor: Colors.transparent,
        tabs: const [
          Tab(text: '登入'),
          Tab(text: '电话注册'),
        ],
      ),
    );
  }

  // 登录视图
  Widget _buildLoginView() {
    return const LoginPage();
  }

  // 注册视图
  Widget _buildRegisterView() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 35),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 22),
          _buildRegisterPhoneField(),
          const SizedBox(height: 22),
          _buildPasswordField(),
          const SizedBox(height: 22),
          _buildConfirmPasswordField(),
          const SizedBox(height: 22),
          _buildRegisterVerificationCodeField(),
          const SizedBox(height: 28),
          _buildAgreementCheckbox(),
          const SizedBox(height: 32),
          _buildRegisterButton(),
          const SizedBox(height: 45),
        ],
      ),
    );
  }

  Widget _buildRegisterPhoneField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '电话号码',
          style: TextStyle(
              fontSize: 14,
              color: Color(0xFF666666),
              fontWeight: FontWeight.w400,
            ),
          ),
          const SizedBox(height: 8),
          Container(
            height: 38,
            decoration: BoxDecoration(
              border: const Border(
                bottom: BorderSide(color: Color(0xFFE0E0E0)),
              ),
            borderRadius: BorderRadius.circular(4),
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12),
                child: DropdownButtonHideUnderline(
                  child: DropdownButton<String>(
                    value: _selectedCountryCode,
                    items: _countryCodes.map((String code) {
                      String label;
                      switch (code) {
                        case '+86':
                          label = '+86 (中国大陆)';
                          break;
                        case '+853':
                          label = '+853 (澳门)';
                          break;
                        case '+852':
                          label = '+852 (香港)';
                          break;
                        default:
                          label = '($code)';
                      }
                      return DropdownMenuItem<String>(
                        value: code,
                        child: Text(
                          '($code)',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            fontSize: 14,
                            color: const Color(0xFF333333),
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      );
                    }).toList(),
                    onChanged: (String? newValue) {
                      if (newValue != null) {
                        setState(() {
                          _selectedCountryCode = newValue;
                        });
                      }
                    },
                    icon: Icon(
                      Icons.arrow_drop_down,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                      size: 20,
                    ),
                    isDense: true,
                    elevation: 8,
                    dropdownColor: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                    menuMaxHeight: 200,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontSize: 14,
                      color: const Color(0xFF333333),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
              Expanded(
                child: TextField(
                  controller: _registerPhoneController,
                  textAlignVertical: TextAlignVertical.center, // 确保文本垂直居中
                  style: const TextStyle(
                    fontSize: 14,
                    color: Color(0xFF333333),
                  ),
                  decoration: const InputDecoration(
                    border: InputBorder.none,
                    contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 12), // 添加垂直padding控制
                    hintText: '',
                    isDense: true, // 减少默认的垂直padding
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildPasswordField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '登入密码',
          style: TextStyle(
            fontSize: 14,
            color: Color(0xFF666666),
            fontWeight: FontWeight.w400,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          height: 44,
          decoration: BoxDecoration(
            border: const Border(
              bottom: BorderSide(color: Color(0xFFE0E0E0)),
            ),
            borderRadius: BorderRadius.circular(4),
          ),
          child: TextField(
            controller: _passwordController,
            obscureText: true,
            style: const TextStyle(
              fontSize: 14,
              color: Color(0xFF333333),
            ),
            decoration: const InputDecoration(
              border: InputBorder.none,
              contentPadding: EdgeInsets.symmetric(horizontal: 12),
              hintText: '请输入登录密码',
              hintStyle: TextStyle(
                fontSize: 14,
                color: Color(0xFFCCCCCC),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildConfirmPasswordField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '确认密码',
          style: TextStyle(
            fontSize: 14,
            color: Color(0xFF666666),
            fontWeight: FontWeight.w400,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          height: 44,
          decoration: BoxDecoration(
            border: const Border(
              bottom: BorderSide(color: Color(0xFFE0E0E0)),
            ),
            borderRadius: BorderRadius.circular(4),
          ),
          child: TextField(
            controller: _confirmPasswordController,
            obscureText: true,
            style: const TextStyle(
              fontSize: 14,
              color: Color(0xFF333333),
            ),
            decoration: const InputDecoration(
              border: InputBorder.none,
              contentPadding: EdgeInsets.symmetric(horizontal: 12),
              hintText: '请再次确认密码',
              hintStyle: TextStyle(
                fontSize: 14,
                color: Color(0xFFCCCCCC),
              ),
            ),
          ),
        ),
      ],
    );
  }
  Widget _buildRegisterVerificationCodeField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '验证码',
          style: TextStyle(
            fontSize: 14,
            color: Color(0xFF666666),
            fontWeight: FontWeight.w400,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          height: 44,
          decoration: BoxDecoration(
            border: const Border(
              bottom: BorderSide(color: Color(0xFFE0E0E0)),
            ),
            borderRadius: BorderRadius.circular(4),
          ),
          child: Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _registerVerificationCodeController,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Color(0xFF333333),
                  ),
                  decoration: const InputDecoration(
                    border: InputBorder.none,
                    contentPadding: EdgeInsets.symmetric(horizontal: 12),
                    hintText: '请输入验证码',
                    hintStyle: TextStyle(
                      fontSize: 14,
                      color: Color(0xFFCCCCCC),
                    ),
                  ),
                ),
              ),
              Container(
                margin: const EdgeInsets.only(right: 8),
                child: TextButton(
                  onPressed: () {
                    // 获取验证码逻辑
                  },
                  style: TextButton.styleFrom(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    minimumSize: Size.zero,
                    tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                  child: const Text(
                    '获取验证',
                    style: TextStyle(
                      fontSize: 14,
                      color: Color(0xFF9C7FD9),
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildAgreementCheckbox() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        GestureDetector(
          onTap: () {
            setState(() {
              _isAgreed = !_isAgreed;
            });
          },
          child: Container(
            width: 20,
            height: 20,
            decoration: BoxDecoration(
              color: _isAgreed ? const Color(0xFF9C7FD9) : Colors.transparent,
              border: Border.all(
                color: _isAgreed ? const Color(0xFF9C7FD9) : const Color(0xFFCCCCCC),
                width: 1.5,
              ),
              borderRadius: BorderRadius.circular(44),
            ),
            child: _isAgreed
                ? const Icon(
                    Icons.check,
                    size: 14,
                    color: Colors.white,
                  )
                : null,
          ),
        ),
        const SizedBox(width: 8),
        const Expanded(
          child: Text(
            '我已阅读并同意《用户协议》',
            style: TextStyle(
              fontSize: 14,
              color: Color(0xFF999999),
              fontWeight: FontWeight.w400,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildRegisterButton() {
    return Container(
      width: double.infinity,
      height: 50,
      decoration: BoxDecoration(
        gradient: _isAgreed
          ? const LinearGradient(
              colors: [Color(0xFF9C7FD9), Color(0xFF8B6FD9)],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            )
          : null,
        color: _isAgreed ? null : const Color(0xFFCCCCCC),
        borderRadius: BorderRadius.circular(25),
        boxShadow: _isAgreed ? [
          BoxShadow(
            color: const Color(0xFF9C7FD9).withOpacity(0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ] : null,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: _isAgreed ? () {
            // 注册逻辑
          } : null,
          borderRadius: BorderRadius.circular(25),
          child: const Center(
            child: Text(
              '注册',
              style: TextStyle(
                fontSize: 17,
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
      ),
    );
  }

}
