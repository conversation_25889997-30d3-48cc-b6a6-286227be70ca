import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import '../lib/me/user_info/user_retrieve_password_page.dart';

void main() {
  group('RetrievePasswordPage Tests', () {
    testWidgets('should display all required fields', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: RetrievePasswordPage(),
        ),
      );

      // 验证页面标题
      expect(find.text('找回密码'), findsOneWidget);

      // 验证所有字段标签
      expect(find.text('电话'), findsOneWidget);
      expect(find.text('登入密码'), findsOneWidget);
      expect(find.text('确认密码'), findsOneWidget);
      expect(find.text('验证码'), findsOneWidget);

      // 验证按钮
      expect(find.text('修改密码'), findsOneWidget);
      expect(find.text('获取验证码'), findsOneWidget);

      // 验证输入框占位符
      expect(find.text('请输入新密码'), findsOneWidget);
      expect(find.text('请再次确认密码'), findsOneWidget);
      expect(find.text('请输入验证码'), findsOneWidget);
    });

    testWidgets('should show error dialog when password fields are empty', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: RetrievePasswordPage(),
        ),
      );

      // 清空手机号码字段（因为有默认值）
      await tester.enterText(find.byType(TextField).at(0), '');

      // 点击修改密码按钮
      await tester.tap(find.text('修改密码'));
      await tester.pumpAndSettle();

      // 验证错误对话框出现
      expect(find.text('提示'), findsOneWidget);
      expect(find.text('请输入手机号码'), findsOneWidget);
    });

    testWidgets('should toggle password visibility', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: RetrievePasswordPage(),
        ),
      );

      // 查找密码可见性切换按钮
      final visibilityButtons = find.byIcon(Icons.visibility_off);
      expect(visibilityButtons, findsNWidgets(2)); // 两个密码字段

      // 点击第一个密码字段的可见性按钮
      await tester.tap(visibilityButtons.first);
      await tester.pumpAndSettle();

      // 验证图标变为可见状态
      expect(find.byIcon(Icons.visibility), findsOneWidget);
    });

    testWidgets('should validate password confirmation', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: RetrievePasswordPage(),
        ),
      );

      // 输入不同的密码
      await tester.enterText(find.byType(TextField).at(1), 'password123');
      await tester.enterText(find.byType(TextField).at(2), 'password456');
      await tester.enterText(find.byType(TextField).at(0), '66129907');
      await tester.enterText(find.byType(TextField).at(3), '1234');

      // 点击修改密码按钮
      await tester.tap(find.text('修改密码'));
      await tester.pumpAndSettle();

      // 验证密码不一致错误
      expect(find.text('两次输入的密码不一致'), findsOneWidget);
    });
  });
}
