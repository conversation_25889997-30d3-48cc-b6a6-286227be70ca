<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>

<head>
    <title>开放平台qrcode</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <script type="text/javascript" src="../../js/jquery-3.3.1.min.js"></script>
    <script type="text/javascript">
        function ajax_send(func) {
            $.ajax({
                url: 'query_qrcode.php?' + $("#ori").val(),
                type: "get",
                success: function(text) {
                    var data = JSON.parse(text);
                    if (data.is_success == "T") {
                        $("#result").append("查询结果为:" + data.data.trans_status + "</br>");
                    } else {
                        $("#result").append("查询失败</br>");
                    }
                    func.call(this, data);
                }
            });
        }
        $(function() {
            var interval = setInterval(function() {
                ajax_send(function(data) {
                    if (data.data.trans_status == "SUCCESS") {
                        clearInterval(interval);
                    };
                });
            }, 3000); //每隔3秒调用一次函数

        });
    </script>
    <style>
        * {
            margin: 0;
            padding: 0;
        }

        ul,
        ol {
            list-style: none;
        }

        .bank-list li {
            float: left;
            width: 153px;
            margin-bottom: 5px;
        }

        #main {
            width: 750px;
            margin: 0 auto;
            font-size: 14px;
            font-family: '宋体';
        }

        .content {
            margin-top: 5px;
        }

        .content dt {
            width: 130px;
            display: inline-block;
            text-align: right;
            float: left;

        }

        .content dd {
            margin-left: 100px;
            margin-bottom: 5px;
        }

        #foot {
            margin-top: 10px;
        }

        .foot-ul li {
            text-align: center;
        }

        .note-help {
            color: #999999;
            font-size: 12px;
            line-height: 130%;
            padding-left: 3px;
        }

        .cashier-nav ol li {
            float: left;
        }

        .alipay_link a:link {
            text-decoration: none;
            color: #8D8D8D;
        }

        .alipay_link a:visited {
            text-decoration: none;
            color: #8D8D8D;
        }
    </style>
</head>

<body text=#000000 bgColor="#ffffff" leftMargin=0 topMargin=4>
    <?php
    if ($_POST) {

        $req = $_POST["req"];
        $result = $_POST["result"];

        $content = json_decode($result, true);
        $qrcode = $content["data"]["qr_code"];

        include_once "../../../phpqrcode/phpqrcode.php";
        $url = $qrcode;
        $errorCorrectionLevel = 'L'; //容错级别
        $matrixPointSize = 6; //生成图片大小
        //生成二维码图片
        QRcode::png($url, 'qrcode/qrcode.png', $errorCorrectionLevel, $matrixPointSize, 2);

        $logo = 'qrcode/mpay.png'; //准备好的logo图片

        $QR = 'qrcode/qrcode.png'; //已经生成的原始二维码图

        if ($logo !== FALSE) {
            $QR = imagecreatefromstring(file_get_contents($QR));
            $logo = imagecreatefromstring(file_get_contents($logo));

            $QR_width = imagesx($QR); //二维码图片宽度

            $QR_height = imagesy($QR); //二维码图片高度

            $logo_width = imagesx($logo); //logo图片宽度

            $logo_height = imagesy($logo); //logo图片高度

            $logo_qr_width = $QR_width / 5;

            $scale = $logo_width / $logo_qr_width;

            $logo_qr_height = $logo_height / $scale;

            $from_width = ($QR_width - $logo_qr_width) / 2;

            //重新组合图片并调整大小

            imagecopyresampled(
                $QR,
                $logo,
                $from_width,
                $from_width,
                0,
                0,
                $logo_qr_width,

                $logo_qr_height,
                $logo_width,
                $logo_height
            );
        }

        //输出图片
        $image = 'qrcode/mpay_qrcode' . date("YmdHis") . '.png';
        imagepng($QR, $image);




    ?>
        <div id="main">
            <form name=query method=post target="_blank">
                <div id="body" style="clear:left">
                    <dl class="content">
                        <img style='display:block;margin:0 auto' src="<?=$image?>"></img>
                    </dl>
                </div>
                <input type="hidden" id="ori" name="ori" value="<?= $req ?>" />
                <div id="result" style="height:400px;overflow:hidden;overflow-y: scroll">
                    开始查询结果(间隔3秒)</br>
                </div>
            </form>
            <div id="foot">
                <ul class="foot-ul">
                    <li>
                        <font class="note-help">如果您点击“确认”按钮，即表示您同意该次的执行操作。 </font>
                    </li>
                    <li>
                        澳门通版权所有 2011-2015 MACAUPAY.COM
                    </li>
                </ul>
            </div>
        </div>

    <?php } ?>
</body>

</html>