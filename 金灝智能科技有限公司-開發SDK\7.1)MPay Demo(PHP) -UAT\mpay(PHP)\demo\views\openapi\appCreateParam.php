
<?php
if ($_POST) {

    include_once "../../../sdk/Openapi.php";

    $version = $_POST["WIDversion"];
    $sign_type = $_POST["WIDsign_type"];
    $org_id = $_POST["WIDorg_id"];

    $data = [
        "notify_url" => $_POST["WIDnotify_url"],
		"body" => $_POST["WIDbody"],

		"currency" => $_POST["WIDcurrency"],
        "extend_params" => $_POST["WIDextend_params"],
		"out_trans_id" => $_POST["WIDout_trans_id"],
		"passback_parameters" => $_POST["WIDpassback_parameters"],
        "subject" => $_POST["WIDsubject"],
		"total_fee" => $_POST["WIDtotal_fee"],
		"it_b_pay" => $_POST["WIDit_b_pay"],

        "product_code" => $_POST["WIDproduct_code"],
		"pay_channel" => $_POST["WIDpay_channel"],
		"sub_appid" => $_POST["WIDsub_appid"],
        "goods_detail" => $_POST["WIDgoods_detail"],
		"risk_info" => $_POST["WIDrisk_ino"],

	];

    $openapi = new Openapi($org_id, $version, $sign_type);
    $returnvalue = $openapi->pay_mobile($data);

    echo $returnvalue;
    exit;
}
?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
	<head>
	<title>开放平台APP下单接口</title>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<style>
*{
	margin:0;
	padding:0;
}
ul,ol{
	list-style:none;
}
.title{
    color: #ADADAD;
    font-size: 14px;
    font-weight: bold;
    padding: 8px 16px 5px 10px;
}

.new-btn-login-sp{
	border:1px solid #D74C00;
	padding:1px;
	display:inline-block;
}

.new-btn-login{
    background-color: #ff8c00;
	color: #FFFFFF;
    font-weight: bold;
	border: medium none;
	width:82px;
	height:28px;
}
.new-btn-login:hover{
    background-color: #ffa300;
	width: 82px;
	color: #FFFFFF;
    font-weight: bold;
    height: 28px;
}
.bank-list li{
	float:left;
	width:153px;
	margin-bottom:5px;
}

#main{
	width:750px;
	margin:0 auto;
	font-size:14px;
	font-family:'宋体';
}
.null-star{
	color:#fff;
}
.content{
	margin-top:5px;
}

.content dt{
	width:130px;
	display:inline-block;
	text-align:right;
	float:left;
	
}
.content dd{
	margin-left:100px;
	margin-bottom:5px;
}
.content dd select{
    width:34%;
}
#foot{
	margin-top:10px;
}
.foot-ul li {
	text-align:center;
}
.note-help {
    color: #999999;
    font-size: 12px;
    line-height: 130%;
    padding-left: 3px;
}

.cashier-nav {
    font-size: 14px;
    margin: 15px 0 10px;
    text-align: left;
    height:30px;
    border-bottom:solid 2px #CFD2D7;
}
.cashier-nav ol li {
    float: left;
}
.cashier-nav li.current {
    color: #AB4400;
    font-weight: bold;
}
.cashier-nav li.last {
    clear:right;
}
.alipay_link {
    text-align:right;
}
.alipay_link a:link{
    text-decoration:none;
    color:#8D8D8D;
}
.alipay_link a:visited{
    text-decoration:none;
    color:#8D8D8D;
}
</style>
</head>
<body text=#000000 bgColor="#ffffff" leftMargin=0 topMargin=4>
	<div id="main">
		<div id="head">
            <dl class="alipay_link">
                <a target="_blank" href=""><span>帮助中心</span></a>
            </dl>
            <span class="title">开放平台APP下单接口快速通道</span>
		</div>
        <div class="cashier-nav">
            <ol>
				<li class="current">1、确认信息 →</li>
				<li>2、点击确认 →</li>
				<li class="last">3、确认完成</li>
            </ol>
        </div>
        <form name=precreate action="" method=post target="_blank">
            <div id="body" style="clear:left">
                <dl class="content">
                    <dt>org_id：</dt>
                    <dd>
                        <span class="null-star">*</span>
                        <input size="30" name="WIDorg_id" value="888534816999848"/>
                        <span>[机构编号]</span>
                    </dd>
					<dt>version：</dt>
					<dd>
						<span class="null-star">*</span>
						<input size="30" name="WIDversion" value="1.1.0"/>
						<span>[版本號]</span>
					</dd>
					<dt>sign_type：</dt>
					<dd>
						<span class="null-star">*</span>
						<select  name="WIDsign_type">
							<option  selected value="RSA2">RSA2</option>
							<option  value="MD5">MD5</option>
						</select>
						<span>[簽名類型]</span>
					</dd>
					<dt>pay_channel：</dt>
					<dd>
						<span class="null-star">*</span>
						<select name="WIDpay_channel" onchange="change()">
							<option  value="alipay">alipay-支付宝</option>
							<option  selected value="mpay">mpay-澳門錢包</option>
							<option  value="wechat">wechat-微信</option>
						</select>
						<span>[支付渠道]</span>
					</dd>
                    <dt>sub_appid：</dt>
                    <dd>
                        <span class="null-star">*</span>
                        <input size="30" name="WIDsub_appid" value="wxbd9b074bd5c50fdc" />
                        <span>[商户微信公众号ID,pay_channel为微信时必传]</span>
                    </dd>
					<dt>notify_url：</dt>
					<dd>
						<span class="null-star">*</span>
						<input size="30" name="WIDnotify_url" value="https://sitopenapi.macaupay.com.mo/mpay/notify.do"/>
						<span>[异步通知回调地址]</span>
					</dd>
					<dt>return_url：</dt>
					<dd>
						<span class="null-star">*</span>
						<input size="30" name="WIDreturn_url" value="https://www.baidu.com"/>
						<span>[商戶同步回调地址]</span>
					</dd>
					<dt>out_trans_id：</dt>
					<dd>
						<span class="null-star">*</span>
						<input size="30" name="WIDout_trans_id" value="<?=date("YmdHis")?>"/>
						<span>[商家网站中的唯一订单ID]</span>
					</dd>
					<dt>subject：</dt>
					<dd>
						<span class="null-star">*</span>
						<input size="30" name="WIDsubject" value="正掃付款++" />
						<span>[商品名称/交易牌/订单主题/订单关键词等]</span>
					</dd>
					<dt>product_code：</dt>
					<dd>
						<span class="null-star">*</span>
						<input size="30" name="WIDproduct_code" value="MP_APP_PAY" />
						<span>[產品代碼]</span>
					</dd>
					<dt>total_fee：</dt>
					<dd>
						<span class="null-star">*</span>
						<input size="30" name="WIDtotal_fee" value="0.1"/>
						<span>[此订单总费用]</span>
					</dd>
					<dt>body：</dt>
					<dd>
						<span class="null-star">*</span>
						<input size="30" name="WIDbody" value=""/>
						<span>[交易的具体描述]</span>
					</dd>
					<dt>currency：</dt>
					<dd>
						<span class="null-star">*</span>
						<input size="30" name="WIDcurrency" value="MOP"/>
						<span>[交易的定价货币]</span>
					</dd>
					<dt>passback_parameters：</dt>
					<dd>
						<span class="null-star">*</span>
						<input size="30" name="WIDpassback_parameters" value="回傳參數" />
						<span>[平台将通过异步通知回传此参数]</span>
					</dd>
                    <dt>operator_id：</dt>
                    <dd>
                        <span class="null-star">*</span>
                        <input size="30" name="WIDoperator_id" value="Yx_001"/>
                        <span>[操作员编号]</span>
                    </dd>
                    <dt>it_b_pay：</dt>
                    <dd>
                        <span class="null-star">*</span>
                        <input size="30" name="WIDit_b_pay" value="5m"/>
                        <span>[订单有效时间]</span>
                    </dd>
					<dt>extend_params：</dt>
					<dd>
						<span class="null-star">*</span>
						<input size="30" name="WIDextend_params" value="{&quot;sub_merchant_name&quot;:&quot;網絡零售測試&quot;,&quot;sub_merchant_id&quot;:&quot;888534816999848&quot;,&quot;sub_merchant_industry&quot;:&quot;4816&quot;,&quot;store_id&quot;:&quot;0001&quot;,&quot;store_name&quot;:&quot;澳門店&quot;}" />
						<span>[用于传输商家的特定商业信息]</span>
					</dd>
                    <dt>goods_detail：</dt>
                    <dd>
                        <span class="null-star">*</span>
                        <textarea rows="3" cols="50" name="WIDgoods_detail">[{"goods_id":"apple-01","goods_name":" iphone","goods_category":"33","price":"0.1","quantity":"1"}]</textarea>
                    </dd>
                    <dt>risk_ino：</dt>
                    <dd>
                        <span class="null-star">*</span>
                        <textarea rows="10" cols="50" name="WIDrisk_ino">{"session_id":"26dxxxxxxxxxxxxxxxxxxxxxxxxx03e9","serial_no":"f0xxxc9a","client_ip":"************","latitude_longitude":"39.9151190000,116.4039630000","idfa":"5D08BADB6-B7D1-46DE-BDAB-B66468A1EFCC","terminal_type":"APP","device_name":"Mike's iPhone","device_brand":"APPLE","device_model":"iPhone 7 Plus","imei":"863xxxxxxxx5012","os_name":"ios","os_version":"9.1.1","wireless_network":"china unicom","wireless_carrier":"china unicom","flight_mode":"enabled","finger_print_enabled":"enabled","device_boot_time":"20200617083001","last_unlock_time":"20200617083001","screen_resolution":"1024*768","is_jailbreaked":"true","mac_address":"8c:be:be:71:1f:34","system_language":"EN","time_zone":"UTC+08","signup_time":"20200617083001","last_login_time":"20200617083001","merchant_user_id":"20200617083001"}</textarea>
                    </dd>
                    <dt></dt>
                    <dd>
                        <span class="new-btn-login-sp">
                            <button class="new-btn-login" type="submit" style="text-align:center;">确 认</button>
                        </span>
                    </dd>
                </dl>
            </div>
		</form>
        <div id="foot">
			<ul class="foot-ul">
				<li><font class="note-help">如果您点击“确认”按钮，即表示您同意该次的执行操作。 </font></li>
				<li>
					© 2020 Macau Pass S.A.
				</li>
			</ul>
		</div>
	</div>
</body>
</html>