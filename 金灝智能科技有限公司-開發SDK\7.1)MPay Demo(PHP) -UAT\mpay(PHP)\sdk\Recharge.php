<?php

include_once "Base.php";


/**
 * 扫码充值接口
 */
class Recharge extends Base
{


    /**
     * 構造函數，初始化參數
     * 
     */
    public function __construct($org_id, $version, $sign_type)
    {
        $this->_ord_id = $org_id;
        $this->_version = $version;
        $this->_sign_type = $sign_type;
        parent::__construct();
    }

    /**
     * 掃碼充值
     * mpay.trade.recharge
     */
    public function recharge($value)
    {
        $data = $this->get_head("mpay.trade.recharge");

        $data["out_trans_id"] = $value["out_trans_id"];
        $data["currency"] = $value["currency"];
        $data["trans_amount"] = $value["trans_amount"];
        $data["identity_code_type"] = $value["identity_code_type"];
        $data["buyer_identity_code"] = $value["buyer_identity_code"];
        $data["biz_type"] = $value["biz_type"];
        $data["extend_params"] = $value["extend_params"];
        return $this->buildRequest($data);
    }

    /**
     * 掃碼充值查詢
     * mpay.trade.recharge.query
     */
    public function query($value)
    {
        $data = $this->get_head("mpay.trade.recharge.query");

        if (isset($value["out_trans_id"])) {
            $data["out_trans_id"] = $value["out_trans_id"];
        }
        if (isset($value["trans_id"])) {
            $data["trans_id"] = $value["trans_id"];
        }

        return $this->buildRequest($data);
    }

    /**
     * 掃碼充值沖正
     * mpay.trade.recharge.cancel
     */
    public function cancel($value)
    {
        $data = $this->get_head("mpay.trade.recharge.cancel");

        if (isset($value["out_trans_id"])) {
            $data["out_trans_id"] = $value["out_trans_id"];
        }
        if (isset($value["trans_id"])) {
            $data["trans_id"] = $value["trans_id"];
        }

        return $this->buildRequest($data);

    }
}