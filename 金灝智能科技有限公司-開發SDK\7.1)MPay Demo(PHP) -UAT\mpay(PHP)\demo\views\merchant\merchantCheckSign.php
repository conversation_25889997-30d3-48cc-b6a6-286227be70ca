<?php
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (false !== strpos($_SERVER["CONTENT_TYPE"], 'application/json')) {
        $content = file_get_contents('php://input');
        $post    = (array)json_decode($content, true);

        include_once "../../../util/ParameterFormat.php";
        include_once "../../../sdk/Openapi.php";
        $requestBody = $post["requestBody"];
        $requestBody=json_decode($requestBody,true);
        if (!is_array($requestBody)) {
            $data = [
                "is_success" => "F",
                "error" => "請按json格式輸入"
            ];
        } else {
            //參數排序
            $array = ParameterFormat::sort($requestBody, false);
            //獲取預簽名字符串
            $link_string = ParameterFormat::link_string($array);
            $openapi = new Openapi("", "", $post["signType"]);
            $test=$openapi->sign_test($requestBody);
            $data = [
                "is_success" => "T",
                "data" => [
                    "preStr" =>  $link_string,
                    "sign" => $test["sign"]
                ],
                "error" => ""
            ];
        }
        echo json_encode($data, JSON_UNESCAPED_UNICODE);
        exit;
    }
}
?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>

<head>
    <title>开放平台-簽名驗證</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <script type="text/javascript" src="../../js/jquery-3.3.1.min.js"></script>
    <script type="text/javascript">
        function ajax_send() {
            $("textarea[name='preStr']").val("");
            $("textarea[name='sign']").val("");

            var body = {};
            var requestBody = $("textarea[name='requestBody']").val();
            var signType = $('input:radio[name="signType"]:checked').val();
            body['requestBody'] = requestBody;
            body['signType'] = signType;
            $.ajax({
                url: '/mpay/demo/views/merchant/merchantCheckSign.php',
                type: "POST",
                data: JSON.stringify(body),
                contentType: 'application/json;charset=UTF-8',
                success: function(data) {
                    //alert(text)
                    //var data=JSON.parse(text);
                    if (data.is_success == "T") {
                        $("textarea[name='preStr']").val(data.data.preStr);
                        $("textarea[name='sign']").val(data.data.sign);
                    } else {
                        alert(data.error)
                    }
                },
                dataType: "json"
            });
        }
    </script>
    <style>
        * {
            margin: 0;
            padding: 0;
        }

        ul,
        ol {
            list-style: none;
        }

        .title {
            color: #ADADAD;
            font-size: 14px;
            font-weight: bold;
            padding: 8px 16px 5px 10px;
        }

        .new-btn-login-sp {
            border: 1px solid #D74C00;
            padding: 1px;
            display: inline-block;
        }

        .new-btn-login {
            background-color: #ff8c00;
            color: #FFFFFF;
            font-weight: bold;
            border: medium none;
            width: 82px;
            height: 28px;
        }

        .new-btn-login:hover {
            background-color: #ffa300;
            width: 82px;
            color: #FFFFFF;
            font-weight: bold;
            height: 28px;
        }

        .bank-list li {
            float: left;
            width: 153px;
            margin-bottom: 5px;
        }

        #main {
            width: 750px;
            margin: 0 auto;
            font-size: 14px;
            font-family: '宋体';
        }

        .content dd select {
            width: 34%;
        }

        .null-star {
            color: #fff;
        }

        .content {
            margin-top: 5px;
        }

        .content dt {
            width: 130px;
            display: inline-block;
            text-align: right;
            float: left;

        }

        .content dd {
            margin-left: 100px;
            margin-bottom: 5px;
        }

        #foot {
            margin-top: 10px;
        }

        .foot-ul li {
            text-align: center;
        }

        .note-help {
            color: #999999;
            font-size: 12px;
            line-height: 130%;
            padding-left: 3px;
        }

        .cashier-nav {
            font-size: 14px;
            margin: 15px 0 10px;
            text-align: left;
            height: 30px;
            border-bottom: solid 2px #CFD2D7;
        }

        .cashier-nav ol li {
            float: left;
        }

        .cashier-nav li.current {
            color: #AB4400;
            font-weight: bold;
        }

        .cashier-nav li.last {
            clear: right;
        }

        .alipay_link {
            text-align: right;
        }

        .alipay_link a:link {
            text-decoration: none;
            color: #8D8D8D;
        }

        .alipay_link a:visited {
            text-decoration: none;
            color: #8D8D8D;
        }
    </style>
</head>

<body text=#000000 bgColor="#ffffff" leftMargin=0 topMargin=4>
    <div id="main">
        <div id="head">
            <dl class="alipay_link">
                <a target="_blank" href=""><span>帮助中心</span></a>
            </dl>
            <span class="title">开放平台扫签名验签工具</span>
        </div>
        <div class="cashier-nav">
            <ol>
                <li class="current">1、确认信息 →</li>
                <li>2、点击确认 →</li>
                <li class="last">3、确认完成</li>
            </ol>
        </div>
        <form name="checkSign">
            <div id="body" style="clear:left">
                <dl class="content">
                    <dt>请求报文：</dt>
                    <dd>
                        <textarea rows="10" cols="80" name="requestBody"></textarea>
                    </dd>
                    <dt></dt>
                    <dd>
                    </dd>
                    <dt>签名方式：</dt>
                    <dd>
                        <span class="null-star">*</span>
                        <input type="radio" name="signType" checked value='MD5'>MD5
                        <input type="radio" name="signType" value='RSA2'>RSA2
                        <span>[签名方式]</span>
                    </dd>
                    <dt></dt>
                    <dd>
                        <span class="new-btn-login-sp">
                            <button class="new-btn-login" type="button" onclick="ajax_send()" style="text-align:center;">开始签名</button>
                        </span>
                    </dd>
                    <dt>待签名内容：</dt>
                    <dd>
                        <textarea rows="10" cols="80" name="preStr"></textarea>
                    </dd>
                    <dt>签名(sign)：</dt>
                    <dd>
                        <textarea rows="5" cols="80" name="sign"></textarea>
                    </dd>
                </dl>
            </div>
        </form>
    </div>
</body>

</html>