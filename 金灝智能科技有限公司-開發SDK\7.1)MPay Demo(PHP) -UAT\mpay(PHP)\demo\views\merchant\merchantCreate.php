<?php
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
	if (false !== strpos($_SERVER["CONTENT_TYPE"], 'application/json')) {
		$content = file_get_contents('php://input');
		$post    = (array)json_decode($content, true);

		include_once "../../../sdk/Openapi.php";

		$version = "1.0.0";
		$sign_type = $post["sign_type"];
		$org_id = $post["org_id"];

		$data = [
			"notify_url" => "https://sitopenapi.macaupay.com.mo/mpay/notify.do",
			"return_url" => "https://www.baidu.com",
			"body" => "正掃付款+%",

			"currency" => "MOP",
			"extend_params" => json_encode($post["extend_params"],JSON_UNESCAPED_UNICODE),
			"out_trans_id" => date("YmdHis"),
			"passback_parameters" => "回傳參數",
			"subject" => "正掃付款++",
			"total_fee" => $post["total_fee"],
			"it_b_pay" => "5m",

			"product_code" => "MP_APP_PAY",
			"pay_channel" => $post["pay_channel"],
			"sub_appid" => $post["sub_appid"],
			"goods_detail" => "",
			"risk_info" => ""
		];
		$openapi = new Openapi($org_id, $version, $sign_type);
		$returnvalue = $openapi->pay_mobile_testsign($data);
		$data = [
			"respCode" => "0000", 
			"signData" => json_encode($returnvalue,JSON_UNESCAPED_UNICODE),
			"respMsg"=>"交易成功"
		];

		echo json_encode($data,JSON_UNESCAPED_UNICODE);
		exit;
	}
}
?>


<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>

<head>
	<title>开放平台模擬商戶下单接口</title>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<script type="text/javascript" src="../../js/jquery-3.3.1.min.js"></script>
	<script type="text/javascript">
		//发送请求
		function ajax_send() {
			var fields = $("form[name=create]").serializeArray();
			var obj = {}; //声明一个对象
			$.each(fields, function(index, field) {
				obj[field.name] = field.value; //通过变量，将属性值，属性一起放到对象中
			})
			$.ajax({
				url: '/mpay/demo/views/merchant/merchantCreate.php',
				type: "POST",
				data: JSON.stringify(obj),
				contentType: 'application/json;charset=UTF-8',
				success: function(data) {
					if (data.respCode == "0000") {
						alert(data.signData)
					} else {
						alert(data.respMsg)
					}
				},
				dataType: "json"
			});
		}
	</script>
	<style>
		* {
			margin: 0;
			padding: 0;
		}

		ul,
		ol {
			list-style: none;
		}

		.title {
			color: #ADADAD;
			font-size: 14px;
			font-weight: bold;
			padding: 8px 16px 5px 10px;
		}

		.new-btn-login-sp {
			border: 1px solid #D74C00;
			padding: 1px;
			display: inline-block;
		}

		.new-btn-login {
			background-color: #ff8c00;
			color: #FFFFFF;
			font-weight: bold;
			border: medium none;
			width: 82px;
			height: 28px;
		}

		.new-btn-login:hover {
			background-color: #ffa300;
			width: 82px;
			color: #FFFFFF;
			font-weight: bold;
			height: 28px;
		}

		.bank-list li {
			float: left;
			width: 153px;
			margin-bottom: 5px;
		}

		#main {
			width: 750px;
			margin: 0 auto;
			font-size: 14px;
			font-family: '宋体';
		}

		.null-star {
			color: #fff;
		}

		.content {
			margin-top: 5px;
		}

		.content dt {
			width: 130px;
			display: inline-block;
			text-align: right;
			float: left;

		}

		.content dd {
			margin-left: 100px;
			margin-bottom: 5px;
		}

		.content dd select {
			width: 34%;
		}

		#foot {
			margin-top: 10px;
		}

		.foot-ul li {
			text-align: center;
		}

		.note-help {
			color: #999999;
			font-size: 12px;
			line-height: 130%;
			padding-left: 3px;
		}

		.cashier-nav {
			font-size: 14px;
			margin: 15px 0 10px;
			text-align: left;
			height: 30px;
			border-bottom: solid 2px #CFD2D7;
		}

		.cashier-nav ol li {
			float: left;
		}

		.cashier-nav li.current {
			color: #AB4400;
			font-weight: bold;
		}

		.cashier-nav li.last {
			clear: right;
		}

		.alipay_link {
			text-align: right;
		}

		.alipay_link a:link {
			text-decoration: none;
			color: #8D8D8D;
		}

		.alipay_link a:visited {
			text-decoration: none;
			color: #8D8D8D;
		}
	</style>
</head>

<body text=#000000 bgColor="#ffffff" leftMargin=0 topMargin=4>
	<div id="main">
		<div id="head">
			<dl class="alipay_link">
				<a target="_blank" href=""><span>帮助中心</span></a>
			</dl>
			<span class="title">开放平台模擬商戶下单接口快速通道</span>
		</div>
		<div class="cashier-nav">
			<ol>
				<li class="current">1、确认信息 →</li>
				<li>2、点击确认 →</li>
				<li class="last">3、确认完成</li>
			</ol>
		</div>
		<form name="create" action="../../merchantSign.do" method=post target="_blank">
			<div id="body" style="clear:left">
				<dl class="content">
					<dt>org_id：</dt>
					<dd>
						<span class="null-star">*</span>
						<input size="30" name="org_id" value="888535999747707" />
						<span>[机构编号]</span>
					</dd>
					<dt>sign_type：</dt>
					<dd>
						<span class="null-star">*</span>
						<select name="sign_type">
							<option selected value="RSA2">RSA2</option>
							<option value="MD5">MD5</option>
						</select>
						<span>[簽名類型]</span>
					</dd>
					<dt>pay_channel：</dt>
					<dd>
						<span class="null-star">*</span>
						<select name="pay_channel">
							<option selected value="alipay">alipay-支付宝</option>
							<option value="mpay">mpay-澳門錢包</option>
							<option value="wechat">wechat-微信</option>
						</select>
						<span>[支付渠道]</span>
					</dd>
					<dt>total_fee：</dt>
					<dd>
						<span class="null-star">*</span>
						<input size="30" name="total_fee" value="5.00" />
						<span>[此订单总费用]</span>
					</dd>
					<dt>sub_appid：</dt>
					<dd>
						<span class="null-star">*</span>
						<input size="30" name="sub_appid" value="wx253578ea80e57302" />
						<span>[商户微信公众号ID,pay_channel为微信时必传]</span>
					</dd>
					<dt>extend_params：</dt>
					<dd>
						<span class="null-star">*</span>
						<input size="30" name="extend_params" value="{&quot;sub_merchant_name&quot;:&quot;河北狗不理包子&quot;,&quot;sub_merchant_id&quot;:&quot;888531098492969&quot;,&quot;sub_merchant_industry&quot;:&quot;4131&quot;}" />
						<span>[用于传输商家的特定商业信息]</span>
					</dd>
					<dt></dt>
					<dd>
						<span class="new-btn-login-sp">
							<button class="new-btn-login" type="button" onclick="ajax_send()" style="text-align:center;">确 认</button>
						</span>
					</dd>
				</dl>
			</div>
		</form>
		<div id="foot">
			<ul class="foot-ul">
				<li>
					<font class="note-help">如果您点击“确认”按钮，即表示您同意该次的执行操作。 </font>
				</li>
				<li>
					© 2020 Macau Pass S.A.
				</li>
			</ul>
		</div>
	</div>
</body>

</html>