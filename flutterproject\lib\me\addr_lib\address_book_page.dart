import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'new_address_page.dart';
import 'edit_address_page.dart';

class AddressBookPage extends StatefulWidget {
  final bool isSelectionMode; // 是否为选择模式

  const AddressBookPage({
    Key? key,
    this.isSelectionMode = false,
  }) : super(key: key);

  @override
  State<AddressBookPage> createState() => _AddressBookPageState();
}

class _AddressBookPageState extends State<AddressBookPage> {
  // 地址数据模型
  final List<AddressItem> addresses = [
    AddressItem(
      name: '凤凰家园9号栋803',
      phone: '历史 17345362345',
      isDefault: true,
    ),
    AddressItem(
      name: '凤凰家园9号栋803',
      phone: '张三 17345362345',
      isDefault: false,
    ),
    AddressItem(
      name: '凤凰家园9号栋803',
      phone: '张三 17345362345',
      isDefault: false,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color.fromARGB(255, 255, 255, 255),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(
            Icons.arrow_back_ios,
            color: Color(0xFF333333),
            size: 20,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          widget.isSelectionMode ? '选择地址' : '地址库',
          style: const TextStyle(
            color: Color(0xFF333333),
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: true,
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.dark,
        ),
      ),
      body: Column(
        children: [
          // 地址列表
          Expanded(
            child: ListView.separated(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              itemCount: addresses.length,
              itemBuilder: (context, index) {
                return _buildAddressItem(addresses[index], index);
              },
              separatorBuilder: (context, index) {
                return Container(
                  height: 1,
                  margin: const EdgeInsets.symmetric(horizontal: 16),
                  color: const Color(0xFFE5E5E5),
                );
              },
            ),
          ),
          // 底部按钮：选择模式下不显示新增按钮
          if (!widget.isSelectionMode)
            Container(
              padding: const EdgeInsets.all(16),
              child: _buildAddButton(),
            ),
        ],
      ),
    );
  }

  // 构建地址项
  Widget _buildAddressItem(AddressItem address, int index) {
    return GestureDetector(
      onTap: widget.isSelectionMode ? () => _selectAddress(address) : null,
      child: Container(
        margin: const EdgeInsets.only(bottom: 12),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: const Color.fromARGB(255, 255, 255, 255).withOpacity(0.04),
              offset: const Offset(0, 2),
              blurRadius: 8,
              spreadRadius: 0,
            ),
          ],
        ),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 地址名称和默认标签
                  Row(
                    children: [
                      Text(
                        address.name,
                        style: const TextStyle(
                          color: Color(0xFF333333),
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      if (address.isDefault) ...[
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 6,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: const Color.fromARGB(255, 255, 255, 255),
                            borderRadius: BorderRadius.circular(4),
                            border: Border.all(
                              color: const Color(0xFFB794F6),
                              width: 1,
                            ),
                          ),
                          child: Text(
                            '默认',
                            style: TextStyle(
                              color: Color(0xFFB794F6).withOpacity(0.8),
                              fontSize: 10,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                  const SizedBox(height: 6),
                  // 联系人和电话
                  Text(
                    address.phone,
                    style: const TextStyle(
                      color: Color(0xFF999999),
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ],
              ),
            ),
            // 根据模式显示不同的按钮
            if (widget.isSelectionMode)
              // 选择模式：显示选择按钮
              Container(
                padding: const EdgeInsets.all(8),
                child: const Icon(
                  Icons.arrow_forward_ios,
                  color: Color(0xFF999999),
                  size: 16,
                ),
              )
            else
              // 普通模式：显示编辑按钮
              GestureDetector(
                onTap: () => _editAddress(index),
                child: Container(
                  padding: const EdgeInsets.all(8),
                  child: const Icon(
                    Icons.edit_outlined,
                    color: Color(0xFF999999),
                    size: 20,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  // 构建新增地址按钮
  Widget _buildAddButton() {
    return GestureDetector(
      onTap: _addNewAddress,
      child: Container(
        width: double.infinity,
        height: 48,
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            colors: [Color(0xFFB794F6), Color(0xFF9F7AEA)],
            begin: Alignment.centerLeft,
            end: Alignment.centerRight,
          ),
          borderRadius: BorderRadius.circular(24),
          boxShadow: [
            BoxShadow(
              color: const Color(0xFFB794F6).withOpacity(0.3),
              offset: const Offset(0, 4),
              blurRadius: 12,
              spreadRadius: 0,
            ),
          ],
        ),
        child: const Center(
          child: Text(
            '新增地址',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ),
    );
  }

  // 编辑地址
  void _editAddress(int index) {
    final address = addresses[index];
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => EditAddressPage(
          initialAddress: address.name.split(' ')[0], // 提取地址部分
          initialRoom: address.name.split(' ').length > 1 ? address.name.split(' ')[1] : '',
          initialContact: address.phone.split(' ')[0],
          initialPhone: address.phone.split(' ')[1],
          initialIsDefault: address.isDefault,
        ),
      ),
    );
  }

  // 选择地址（仅在选择模式下使用）
  void _selectAddress(AddressItem address) {
    if (widget.isSelectionMode) {
      // 返回选中的地址给上一个页面
      Navigator.pop(context, address);
    }
  }

  // 新增地址
  void _addNewAddress() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const NewAddressPage(),
      ),
    );
  }
}

// 地址数据模型
class AddressItem {
  final String name;
  final String phone;
  final bool isDefault;

  AddressItem({
    required this.name,
    required this.phone,
    required this.isDefault,
  });
}
