<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>开放平台预下单接口</title>


    <style>
        .new-btn-login-sp {
            border: 1px solid #D74C00;
            padding: 1px;
            display: inline-block;
        }

        .new-btn-login {
            background-color: #ff8c00;
            color: #FFFFFF;
            font-weight: bold;
            border: medium none;
            width: 82px;
            height: 28px;
        }

        .new-btn-login:hover {
            background-color: #ffa300;
            width: 82px;
            color: #FFFFFF;
            font-weight: bold;
            height: 28px;
        }

        .box {
            padding: 5px 6px 5px 6px;
            background-color: #ffffff;
        }

        .box textarea {
            width: 100%;
            height: 80px;
            padding: 2px 0px;
            margin-left: -6px;
            border: 1px solid gray;
            overflow: auto;
        }
    </style>
</head>

<body>

    <?php
    if ($_POST) {
        include_once "../../../sdk/Openapi.php";
        $version = $_POST["WIDversion"];
        $sign_type = $_POST["WIDsign_type"];
        $org_id = $_POST["WIDorg_id"];


        $data = [
            "out_trans_id" => $_POST["WIDout_trans_id"],
            "notify_url" => $_POST["WIDnotify_url"],
            "return_url" => $_POST["WIDreturn_url"],
            "body" => $_POST["WIDbody"],
            "currency" => $_POST["WIDcurrency"],
            "extend_params" => $_POST["WIDextend_params"],
            "passback_parameters" => $_POST["WIDpassback_parameters"],
            "price" => $_POST["WIDprice"],
            "quantity" => $_POST["WIDquantity"],
            "show_url" => $_POST["WIDshow_url"],
            "subject" => $_POST["WIDsubject"],
            "total_fee" => $_POST["WIDtotal_fee"],
            "it_b_pay" => $_POST["WIDit_b_pay"],
        ];
        $openapi = new Openapi($org_id, $version, $sign_type);
        $returnvalue = $openapi->precreate($data);
        $content = json_decode($returnvalue, true);
        $str = "sign_type=" . $sign_type . "&out_trans_id=" . $_POST["WIDout_trans_id"] . "&org_id=" . $org_id . "&version=" . $version;

    ?>


        <form name=qrcode action="qrcode_result.php" method=post target="_blank">
            <div class="box">
                <textarea name="result"><?=$returnvalue ?></textarea>
            </div>
            <input type="hidden" name="req" value="<?=$str ?>" />
            <span class="new-btn-login-sp">
                <button class="new-btn-login" type="submit" style="text-align:center;">生成二维码</button>
            </span>
        </form>




    <?php } ?>

</body>

</html>