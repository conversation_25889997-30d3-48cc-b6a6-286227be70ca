<?php
if ($_POST) {
    include_once "../../../sdk/Recharge.php";
    $version = $_POST["version"];
    $sign_type = $_POST["sign_type"];
    $org_id = $_POST["org_id"];


    $data = [
        "out_trans_id" => $_POST["out_trans_id"],
        "trans_id" => $_POST["trans_id"]
    ];

    $recharge = new Recharge($org_id, $version, $sign_type);
    $returnvalue = $recharge->query($data);

    echo $returnvalue;
    exit;
}

?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>开放平台充值查询接口</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <style>
        *{
            margin:0;
            padding:0;
        }
        ul,ol{
            list-style:none;
        }
        .title{
            color: #ADADAD;
            font-size: 14px;
            font-weight: bold;
            padding: 8px 16px 5px 10px;
        }
        .hidden{
            display:none;
        }

        .new-btn-login-sp{
            border:1px solid #D74C00;
            padding:1px;
            display:inline-block;
        }

        .new-btn-login{
            background-color: #ff8c00;
            color: #FFFFFF;
            font-weight: bold;
            border: medium none;
            width:82px;
            height:28px;
        }
        .new-btn-login:hover{
            background-color: #ffa300;
            width: 82px;
            color: #FFFFFF;
            font-weight: bold;
            height: 28px;
        }
        .bank-list{
            overflow:hidden;
            margin-top:5px;
        }
        .bank-list li{
            float:left;
            width:153px;
            margin-bottom:5px;
        }

        #main{
            width:830px;
            margin:0 auto;
            font-size:14px;
            font-family:'宋体';
        }
        #logo{
            background-color: transparent;
            border: medium none;
            background-position:0 0;
            width:166px;
            height:35px;
            float:left;
        }
        .red-star{
            color:#f00;
        }
        .null-star{
            color:#fff;
        }
        .content{
            margin-top:5px;
        }

        .content dt{
            width:160px;
            display:inline-block;
            text-align:right;
            float:left;

        }
        .content dd{
            margin-left:100px;
            margin-bottom:5px;
        }
        #foot{
            margin-top:10px;
        }
        .foot-ul li {
            text-align:center;
        }
        .note-help {
            color: #999999;
            font-size: 12px;
            line-height: 130%;
            padding-left: 3px;
        }

        .cashier-nav {
            font-size: 14px;
            margin: 15px 0 10px;
            text-align: left;
            height:30px;
            border-bottom:solid 2px #CFD2D7;
        }
        .cashier-nav ol li {
            float: left;
        }
        .cashier-nav li.current {
            color: #AB4400;
            font-weight: bold;
        }
        .cashier-nav li.last {
            clear:right;
        }
        .alipay_link {
            text-align:right;
        }
        .alipay_link a:link{
            text-decoration:none;
            color:#8D8D8D;
        }
        .alipay_link a:visited{
            text-decoration:none;
            color:#8D8D8D;
        }
    </style>
</head>
<body text=#000000 bgColor="#ffffff" leftMargin=0 topMargin=4>
<div id="main">
    <div id="head">
        <dl class="alipay_link">
            <a target="_blank" href=""><span>帮助中心</span></a>
        </dl>
        <span class="title">开放平台【充值查询】接口快速通道</span>
    </div>
    <div class="cashier-nav">
        <ol>
            <li class="current">1、确认信息 →&nbsp;</li>
            <li>2、点击确认 →&nbsp;</li>
            <li class="last">3、确认完成</li>
        </ol>
    </div>
    <form name=query action="" method=post target="_blank">
        <div id="body" style="clear:left">
            <dl class="content">
                <dt>org_id：</dt>
                <dd>
                    <span class="red-star">*</span>
                    <input size="40" name="org_id" value="888535999747707"/>
                    <span>[机构编号]</span>
                </dd>
                <dt>version：</dt>
                <dd>
                    <span class="red-star">*</span>
                    <input size="40" name="version" value="1.1.0"/>
                    <span>[版本號]</span>
                </dd>
                <dt>sign_type：</dt>
                <dd>
                    <span class="null-star">*</span>
                    <select  name="sign_type">
                        <option  selected value="RSA2">RSA2</option>
                    </select>
                    <span>[簽名類型]</span>
                </dd>
                <dt>out_trans_id：</dt>
                <dd>
                    <span class="red-star">*</span>
                    <input size="40" name="out_trans_id" />
                    <span>[唯一的商戶系統訂單ID，当trans_id=null时必填]</span>
                </dd>
                <dt>trans_id：</dt>
                <dd>
                    <span class="red-star">*</span>
                    <input size="40" name="trans_id" />
                    <span>[唯一的澳門通交易ID，当out_trans_id=null时必填]</span>
                </dd>
                <dt></dt>
                <dd>
                        <span class="null-star">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
                        <span class="new-btn-login-sp">
                            <button class="new-btn-login" type="submit" style="text-align:center;">确 认</button>
                        </span>
                </dd>
            </dl>
        </div>
    </form>
    <div id="foot">
        <ul class="foot-ul">
            <li>&nbsp;</li>
            <li><font class="note-help">如果您点击“确认”按钮，即表示您同意该次的执行操作。 </font></li>
            <li>&nbsp;</li>
            <li>
                © 2020 Macau Pass S.A.
            </li>
        </ul>
    </div>
</div>
</body>
</html>