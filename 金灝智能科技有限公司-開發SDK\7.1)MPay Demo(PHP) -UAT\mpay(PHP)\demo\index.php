
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>开放平台接口测试</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <script type="text/javascript" src="js/jquery-3.3.1.min.js"></script>
    <script>
        $(document).ready(function(e) {
            SidebarTabHandler.Init();
        });
        var SidebarTabHandler={
            Init:function(){
                $(".tabItemContainer>li").click(function(){
                    $(".tabItemContainer>li>a").removeClass("tabItemCurrent");
                    $(".tabBodyItem").removeClass("tabBodyCurrent");
                    $(this).find("a").addClass("tabItemCurrent");
                    $($(".tabBodyItem")[$(this).index()]).addClass("tabBodyCurrent");
                });
            }
        }
    </script>
    <style>
        *{
            margin:0;
            padding:0;
        }
        ul,ol{
            list-style:none;
        }
        .title{
            color: #ADADAD;
            font-size: 18px;
            font-weight: bold;
            padding: 8px 16px 5px 10px;
        }

        .bank-list li{
            float:left;
            width:153px;
            margin-bottom:5px;
        }

        #main{
            width:850px;
            margin:0 auto;
            font-size:18px;
            font-family:'宋体';
        }

        #foot{
            margin-top:10px;
        }
        .foot-ul li {
            text-align:center;
        }
        .cashier-nav {
            font-size: 18px;
            margin: 15px 0 10px 10px;
            text-align: center;
            height: 520px;
            background-color: #F8F8F8;
        }
        .cashier-nav ol li {
            padding: 10px;
            text-align:left;
            border-bottom: 1px solid #ccc !important;
        }
        .cashier-nav li a {
            text-decoration:none;
        }
        .link {
            text-align:right;
        }
        .link a:link{
            text-decoration:none;
            color:#8D8D8D;
        }
        .link a:visited{
            text-decoration:none;
            color:#8D8D8D;
        }

        #tabMain {
            width: 850px;
            height: 690px;
            margin: 0 auto;
            margin-top: 10px;
            border: 1px solid #ccc;
            -webkit-border-radius: 0 5px 5px 0;
            -moz-border-radius: 0 5px 5px 0;
            border-radius: 0 5px 5px 0;
        }
        .tabItemContainer {
            width: 150px;
            height: 680px;
            float: left;
            text-align: left;
            border: 1px solid #d8cfcf;
            background-color: #e6e6e6;
            margin: 5px 0 5px 5px;
        }
        .tabBodyContainer {
            width: 680px;
            height: 680px;
            float: left;
            background-color: #fff;
            margin: 5px 0 5px 5px;
        }
        .tabItemContainer>li {
            list-style: none;
            text-align: left;
        }
        .tabItemContainer>li>a {
            float: left;
            width: 135px;
            padding: 15px 0 15px 15px;
            font: 16px "微软雅黑", Arial, Helvetica, sans-serif;
            color: #808080;
            cursor: pointer;
            text-decoration: none;
        }
        .tabItemCurrent {
            background-color: #fff;
            border: 1px solid #ccc !important;
            border-right: 1px solid #fff !important;
            position: relative;
            -webkit-border-radius: 5px 0 0 5px;
            -moz-border-radius: 5px 0 0 5px;
            border-radius: 5px 0 0 5px;
        }
        .tabBodyItem {
            position: absolute;
            width: 680px;
            height: 690px;
            display: none;
        }
        .tabBodyItem>p {
            font: 18px "微软雅黑", Arial, Helvetica, sans-serif;
            text-align: center;
            margin: 0 0 5px 10px;
            line-height: 3;
            height: 55px;
            background-color: #F8F8F8;
            border:1px solid #ccc;
        }
        .tabBodyItem>p>a {
            text-decoration: none;
            color: #0F3;
        }
        .tabBodyCurrent{
            display:block;
        }
        .tabItem{
            border: 1px solid #ccc !important;
            border-right: 1px solid #fff !important;
        }
        .item{
            border-bottom: 1px solid #ccc !important;
        }
        .tb{
            height: 610px;
            margin: 0 auto;
            text-align: left;
            border-collapse: collapse;
            width: 100%;
        }

        table,table tr th, table tr td {
            border:1px solid #ccc;
            padding: 5px;
        }
        table tr th,table tr td {
            font: 14px "微软雅黑", Arial, Helvetica, sans-serif;
        }
    </style>
</head>
<body text=#000000 bgColor="#ffffff" leftMargin=0 topMargin=4>
<div id="main">
    <div id="head">
        <dl class="link">
            <a target="_blank" href=""><span>帮助中心</span></a>
        </dl>
        <span class="title">开放平台接口测试快速通道</span>
    </div>
    <div id="tabMain">
        <div class="tabItemContainer">
            <li><a class="tabItem tabItemCurrent">概述</a></li>
            <li><a class="tabItem">商戶秘鑰設置</a></li>
            <li><a class="tabItem">條碼支付(反扫)</a></li>
            <li><a class="tabItem">二維碼支付(正扫)</a></li>
            <li><a class="tabItem">掃碼充值</a></li>
            <li><a class="tabItem">PC支付</a></li>
            <li><a class="tabItem">APP支付</a></li>
            <li><a class="tabItem">H5支付</a></li>
            <li><a class="tabItem">JSAPI支付</a></li>
        </div>
        <div class="tabBodyContainer">
            <div class="tabBodyItem tabBodyCurrent">
                <p>产品对应支持錢包關係</p>
                <div class="cashier-nav">
                    <table class="tb" cellspacing="0">
                        <thead>
                        <tr><th style="width:70px;">产品名称</th>
                            <th style="width:230px;">支持钱包</th>
                            <th>交易場景</th></tr>
                        </thead>
                        <tbody>
                        <tr>
                            <td>條碼支付</td>
                            <td>澳門錢包(MPay),支付寶大陸錢包<br>
                                支付寶香港錢包,支付寶韓國錢包<br>
                                支付寶澳門錢包,澳門國際銀行錢包<br>
                                澳門大豐銀行錢包,澳門廣發銀行錢包<br>
                                微信大陸錢包</td>
                            <td>付款碼支付是指用戶展示錢包APP內的“付款碼”給商戶系統掃描後直接完成支付</td>
                        </tr>
                        <tr>
                            <td>二維碼支付</td>
                            <td>澳門錢包(MPay),微信大陸錢包<br>
                                支付寶大陸錢包,支付寶香港錢包<br></td>
                            <td>用戶打錢包APP中的“掃一掃”功能，掃描商家展示在某收銀場景下的二維碼並進行支付的模式。該模式適用於線下實體店、自助機等場景。</td>
                        </tr>
                        <tr>
                            <td>掃碼充值</td>
                            <td>澳門錢包(MPay)</td>
                            <td>通過掃描客戶手機上MPay的條碼或者輸入客戶MPay的手機號以實現充值。</td>
                        </tr>
                        <tr>
                            <td>WEB支付</td>
                            <td>澳門錢包(MPay),微信大陸錢包<br>
                                支付寶大陸錢包</td>
                            <td>為 PC 網頁提供全套支付解決方案，接入後即可使用MPay、 支付寶、微信、等支付方式完成 PC 網頁的交易。</td>
                        </tr>
                        <tr>
                            <td>WAP支付</td>
                            <td>澳門錢包(MPay),支付寶大陸錢包</td>
                            <td>商家可集成澳門通標準H5支付解決方案，提供用戶熟悉的用戶體驗，同時在支付和結算等方面為商家和消費者提供便利。與澳門通集成之後，商家會在網站支付頁面呈現一個付款按鈕給買家</td>
                        </tr>
                        <tr>
                            <td>APP支付</td>
                            <td>澳門錢包(MPay),微信大陸錢包<br>
                                支付寶大陸錢包</td>
                            <td>只需一个聚合支付 SDK 即可在手机 App 内接入所有主流支付渠道和分期渠道，满足用户多样化的支付需求</td>
                        </tr>
                        <tr>
                            <td>JSAPI支付</td>
                            <td>微信大陸錢包,支付寶大陸錢包</td>
                            <td>在已知用戶信息的前提下,創建訂單,使用js拉取收銀台,然後付款.在該支付解決方案中，需要提前已知用戶信息並且配合js的方式以完成支付</td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="tabBodyItem">
                <p>商戶信息</p>
                <div class="cashier-nav">
                    <ol>
                        <li class="item"><a target="_blank" href="views/merchant/merchantSignSet.php"><span>商戶秘鑰設置</span></a></li>
                        <li><a target="_blank" href="views/merchant/merchantCheckSign.php"><span>簽名驗簽</span></a></li>
                        <li><a target="_blank" href="views/merchant/merchantQueryRate.php"><span>匯率查询</span></a></li>
                    </ol>
                </div>
            </div>
            <div class="tabBodyItem">
                <p>條碼支付(反掃)</p>
                <div class="cashier-nav">
                    <ol>
                        <li><a target="_blank" href="views/openapi/spotpay.php"><span>扫码付款</span></a></li>
                        <li><a target="_blank" href="views/openapi/cancel.php"><span>订单撤销</span></a></li>
                        <li><a target="_blank" href="views/openapi/refund.php"><span>订单退款</span></a></li>
                        <li><a target="_blank" href="views/openapi/query.php"><span>订单查询</span></a></li>
                    </ol>
                </div>
            </div>
            <div class="tabBodyItem">
                <p>二維碼支付(正掃)</p>
                <div class="cashier-nav">
                    <ol>
                        <li><a target="_blank" href="views/openapi/precreate.php"><span>订单预创建</span></a></li>
                        <li><a target="_blank" href="views/openapi/cancel.php"><span>订单撤销</span></a></li>
                        <li><a target="_blank" href="views/openapi/refund.php"><span>订单退款</span></a></li>
                        <li><a target="_blank" href="views/openapi/query.php"><span>订单查询</span></a></li>
                    </ol>
                </div>
            </div>
            <div class="tabBodyItem">
                <p>掃碼充值</p>
                <div class="cashier-nav">
                    <ol>
                        <li><a target="_blank" href="views/recharge/recharge.php"><span>扫码充值</span></a></li>
                        <li><a target="_blank" href="views/recharge/query.php"><span>充值查询</span></a></li>
                        <li><a target="_blank" href="views/recharge/cancel.php"><span>充值撤销</span></a></li>
                    </ol>
                </div>
            </div>
            <div class="tabBodyItem">
                <p>PC支付</p>
                <div class="cashier-nav">
                    <ol>
                        <li><a target="_blank" href="views/openapi/pcPreCreate.php"><span>PC下單(本地收銀台)</span></a></li>
                        <li><a target="_blank" href="views/openapi/pcCreate.php"><span>PC下單(渠道收銀台)</span></a></li>
                        <li><a target="_blank" href="views/openapi/refund.php"><span>订单退款</span></a></li>
                        <li><a target="_blank" href="views/openapi/query.php"><span>订单查询</span></a></li>
                    </ol>
                </div>
            </div>
            <div class="tabBodyItem">
                <p>APP支付</p>
                <div class="cashier-nav">
                    <ol>
                        <li><a target="_blank" href="views/openapi/appCreate.php"><span>APP下單</span></a></li>
                        <li><a target="_blank" href="views/openapi/appCreateParam.php"><span>APP下單參數測試</span></a></li>
                        <li><a target="_blank" href="views/merchant/merchantCreate.php"><span>商戶模擬下單(APP)</span></a></li>
                        <li><a target="_blank" href="views/openapi/refund.php"><span>订单退款</span></a></li>
                        <li><a target="_blank" href="views/openapi/query.php"><span>订单查询</span></a></li>
                    </ol>
                </div>
            </div>
            <div class="tabBodyItem">
                <p>H5支付</p>
                <div class="cashier-nav">
                    <ol>
                        <li><a target="_blank" href="views/openapi/h5Create.php"><span>H5下單</span></a></li>
                        <li><a target="_blank" href="views/openapi/refund.php"><span>订单退款</span></a></li>
                        <li><a target="_blank" href="views/openapi/query.php"><span>订单查询</span></a></li>
                    </ol>
                </div>
            </div>
            <div class="tabBodyItem">
                <p>JSAPI支付</p>
                <div class="cashier-nav">
                    <ol>
                        <li><a target="_blank" href="views/openapi/create.php"><span>订单创建</span></a></li>
                        <li><a target="_blank" href="views/openapi/cancel.php"><span>订单撤销</span></a></li>
                        <li><a target="_blank" href="views/openapi/refund.php"><span>订单退款</span></a></li>
                        <li><a target="_blank" href="views/openapi/query.php"><span>订单查询</span></a></li>
                    </ol>
                </div>
            </div>
        </div>
    </div>
    <div id="foot">
        <ul class="foot-ul">
            <li>
                © 2020 Macau Pass S.A.
            </li>
        </ul>
    </div>
</div>
</body>
</html>