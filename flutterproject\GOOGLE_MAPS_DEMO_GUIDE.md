# 🗺️ Google Maps Demo 测试指南

## ✅ 配置完成状态

### 已完成的配置：
- ✅ Google Maps Flutter 插件已安装
- ✅ API Key 已配置到所有平台：
  - Android: `android/app/src/main/AndroidManifest.xml`
  - iOS: `ios/Runner/Info.plist`
  - Web: `web/index.html`
- ✅ 真实 GoogleMap 组件已启用
- ✅ 测试页面已创建

### API Key 信息：
```
AIzaSyD5GoLHJfmxxEmkxRFNq3gj_LYXNOE3_C4
```

## 🚀 如何测试 Google Maps

### 方法 1：通过主页测试按钮
1. 应用启动后，在主页面可以看到紫色的 "🗺️ Google Maps 测试 Demo" 按钮
2. 点击该按钮进入测试页面
3. 在测试页面点击 "查看订单追踪地图" 按钮
4. 即可看到真实的 Google Maps 显示

### 方法 2：直接导航到订单追踪页面
1. 在应用中导航到订单相关页面
2. 找到订单追踪功能
3. 查看地图区域

## 🎯 地图功能演示

### 地图显示内容：
- **真实的 Google Maps**: 显示澳门地区地图
- **商店标记**: 紫色标记，位置在大三巴附近 (22.198745, 113.543873)
- **目的地标记**: 蓝色标记，位置在 (22.202000, 113.551000)
- **初始视角**: 缩放级别 15，聚焦在商店位置

### 交互功能：
- **地图拖拽**: 可以拖拽地图查看不同区域
- **缩放功能**: 可以双击或手势缩放地图
- **"找我"按钮**: 点击后地图会平移到预设的"我的位置" (22.200500, 113.546000)
- **标记点击**: 可以点击标记查看信息窗口

## 🔧 技术实现细节

### 地图配置：
```dart
GoogleMap(
  initialCameraPosition: const CameraPosition(
    target: LatLng(22.198745, 113.543873), // 商店位置
    zoom: 15,
  ),
  markers: {
    // 商店标记 - 紫色
    Marker(
      markerId: MarkerId('shop'),
      position: LatLng(22.198745, 113.543873),
      infoWindow: InfoWindow(title: '商店'),
      icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueViolet),
    ),
    // 目的地标记 - 蓝色
    Marker(
      markerId: MarkerId('dest'),
      position: LatLng(22.202000, 113.551000),
      infoWindow: InfoWindow(title: '目的地'),
      icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueBlue),
    ),
  },
  onMapCreated: (controller) => _mapController = controller,
  myLocationEnabled: false,
  myLocationButtonEnabled: false,
  zoomControlsEnabled: false,
  mapToolbarEnabled: false,
  compassEnabled: false,
)
```

### "找我"按钮功能：
```dart
Future<void> _goToMe() async {
  final controller = _mapController;
  if (controller == null) return;
  await controller.animateCamera(
    CameraUpdate.newCameraPosition(
      const CameraPosition(target: LatLng(22.200500, 113.546000), zoom: 15),
    ),
  );
}
```

## 📱 平台支持

- ✅ **Web**: 在 Chrome 浏览器中完美运行
- ✅ **Android**: 支持 Android 设备
- ✅ **iOS**: 支持 iOS 设备

## 🎉 预期效果

当你测试时，应该能看到：
1. 真实的 Google Maps 地图显示澳门地区
2. 两个标记点清晰可见
3. 地图可以正常交互（拖拽、缩放）
4. "找我"按钮点击后地图会平滑移动到指定位置
5. 没有任何错误信息

## 🔍 故障排除

如果地图不显示：
1. 检查浏览器控制台是否有 API Key 相关错误
2. 确认网络连接正常
3. 检查 Google Cloud Console 中 API 是否已启用
4. 验证 API Key 是否有使用限制

## 📞 联系信息

如果遇到任何问题，请检查：
- 浏览器开发者工具的控制台
- Flutter 应用的调试输出
- Google Cloud Console 的 API 使用情况
