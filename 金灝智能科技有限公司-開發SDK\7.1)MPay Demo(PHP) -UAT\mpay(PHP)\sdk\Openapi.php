<?php

include_once "Base.php";


/**
 * open api接口
 */
class Openapi extends Base
{

    /**
     * 構造函數，初始化參數
     * 
     */
    public function __construct($org_id, $version, $sign_type)
    {
        $this->_ord_id = $org_id;
        $this->_version = $version;
        $this->_sign_type = $sign_type;
        parent::__construct();
    }


    /**
     * 創建訂單
     * mpay.trade.create
     */
    public function create($value)
    {
        $data = $this->get_head("mpay.trade.create");

        $data["out_trans_id"] = $value["out_trans_id"];
        $data["subject"] = $value["subject"];
        $data["total_fee"] = $value["total_fee"];
        $data["pay_channel"] = $value["pay_channel"];
        $data["sub_appid"] = $value["sub_appid"];
        $data["user_id"] = $value["user_id"];
        $data["body"] = $value["body"];
        $data["currency"] = $value["currency"];
        $data["price"] = $value["price"];
        $data["quantity"] = $value["quantity"];
        $data["extend_params"] = $value["extend_params"];
        $data["it_b_pay"] = $value["it_b_pay"];
        $data["passback_parameters"] = $value["passback_parameters"];
        $data["notify_url"] = $value["notify_url"];

        return $this->buildRequest($data);
    }

    /**
     * 預創建訂單
     * mpay.trade.precreate
     */
    public function precreate($value)
    {
        $data = $this->get_head("mpay.trade.precreate");

        $data["out_trans_id"] = $value["out_trans_id"];
        $data["subject"] = $value["subject"];
        $data["total_fee"] = $value["total_fee"];
        $data["body"] = $value["body"];
        $data["currency"] = $value["currency"];
        $data["price"] = $value["price"];
        $data["show_url"]= $value["show_url"];
        $data["quantity"]= $value["quantity"];
        $data["extend_params"] = $value["extend_params"];
        //$data["goods_detail"] = $value["goods_detail"];
        $data["it_b_pay"] = $value["it_b_pay"];
        $data["passback_parameters"] = $value["passback_parameters"];
        $data["notify_url"] = $value["notify_url"];
        $data["return_url"] = $value["return_url"];

        return $this->buildRequest($data);
    }

    /**
     * 統一下單(收銀台模式)【表單提交】
     * mpay.online.trade.precreate //若使用本地收銀台請置換接口為mpay.trade.precreate
     */
    public function precreate_online($value)
    {
	//若使用本地收銀台請置換接口為mpay.trade.precreate
        $data = $this->get_head("mpay.online.trade.precreate");

        $data["out_trans_id"] = $value["out_trans_id"];
        $data["pay_channel"] = $value["pay_channel"];
        $data["total_fee"] = $value["total_fee"];
        $data["body"] = $value["body"];
        $data["subject"] = $value["subject"];
        $data["currency"] = $value["currency"];
        $data["product_code"] = $value["product_code"];
        $data["extend_params"] = $value["extend_params"];
        $data["goods_detail"] = $value["goods_detail"];
        $data["risk_info"] = $value["risk_info"];
        $data["it_b_pay"] = $value["it_b_pay"];
        $data["passback_parameters"] = $value["passback_parameters"];
        $data["notify_url"] = $value["notify_url"];
        $data["return_url"] = $value["return_url"];

        //表單提交
	//若使用本地收銀台請置換地址為onlinePreCreate.do
        $this->_method = 2;
        return $this->buildRequest($data,"onlineCreate.do","get");
    }


    /**
     * 統一下單(APP)
     * pay.trade.mobile.pay
     */
    public function pay_mobile($value)
    {
        $data = $this->get_head("mpay.trade.mobile.pay");

        $data["out_trans_id"] = $value["out_trans_id"];
        $data["pay_channel"] = $value["pay_channel"];
        $data["total_fee"] = $value["total_fee"];
        $data["body"] = $value["body"];
        $data["subject"] = $value["subject"];
        $data["currency"] = $value["currency"];
        $data["product_code"] = $value["product_code"];
        $data["extend_params"] = $value["extend_params"];
        $data["goods_detail"] = $value["goods_detail"];
        $data["risk_info"] = $value["risk_info"];
        $data["it_b_pay"] = $value["it_b_pay"];
        $data["passback_parameters"] = $value["passback_parameters"];
        $data["notify_url"] = $value["notify_url"];

        //$data["return_url"] = $value["return_url"];
        //$data["sub_appid"] = $value["sub_appid"];

        return $this->buildRequest($data);
    }


    /**
     * 交易撤銷
     * mpay.trade.cancel
     */
    public function cancel($value)
    {
        $data = $this->get_head("mpay.trade.cancel");

        if (isset($value["out_trans_id"])) {
            $data["out_trans_id"] = $value["out_trans_id"];
        }
        if (isset($value["trans_id"])) {
            $data["trans_id"] = $value["trans_id"];
        }


        return $this->buildRequest($data);
    }

    /**
     * 交易退款
     * mpay.trade.refund
     */
    public function refund($value)
    {

        $data = $this->get_head("mpay.trade.refund");

        if (isset($value["out_trans_id"])) {
            $data["out_trans_id"] = $value["out_trans_id"];
        }
        if (isset($value["trans_id"])) {
            $data["trans_id"] = $value["trans_id"];
        }
        $data["out_refund_id"] = $value["out_refund_id"];
        $data["refund_amount"] = $value["refund_amount"];
        $data["refund_reason"] = $value["refund_reason"];

        return $this->buildRequest($data);
    }

    /**
     * 交易查詢
     * mpay.trade.query
     */
    public function query($value)
    {
        $data = $this->get_head("mpay.trade.query");

        if (isset($value["out_trans_id"])) {
            $data["out_trans_id"] = $value["out_trans_id"];
        }
        if (isset($value["trans_id"])) {
            $data["trans_id"] = $value["trans_id"];
        }

        return $this->buildRequest($data);
    }

    /**
     * 匯率查詢
     * mpay.trade.queryRate
     */
    public function query_rate($value)
    {

        $data = $this->get_head("mpay.trade.queryRate");

        $data["in_ccy"] = $value["in_ccy"];
        $data["out_ccy"] = $value["out_ccy"];
        $data["query_date"] = $value["query_date"];

        return $this->buildRequest($data);
    }

    /**
     * 掃碼支付
     * mpay.trade.spotpay
     */
    public function spotpay($value)
    {
        $data = $this->get_head("mpay.trade.spotpay");
        $data["quantity"] = $value["quantity"];
        $data["trans_name"] = $value["trans_name"];
        $data["out_trans_id"] = $value["out_trans_id"];
        $data["currency"] = $value["currency"];
        $data["trans_amount"] = $value["trans_amount"];
        $data["buyer_identity_code"] = $value["buyer_identity_code"];
        $data["identity_code_type"] = $value["identity_code_type"];
        $data["trans_create_time"] = $value["trans_create_time"];
        $data["memo"] = $value["memo"];
        $data["operator_id"] = $value["operator_id"];
        $data["extend_params"] = $value["extend_params"];

        return $this->buildRequest($data);
    }




    /**
     * 統一下單(APP)
     * pay.trade.mobile.pay
     */
    public function pay_mobile_testsign($value)
    {
        $data = $this->get_head("mpay.trade.mobile.pay");

        $data["out_trans_id"] = $value["out_trans_id"];
        $data["pay_channel"] = $value["pay_channel"];
        $data["total_fee"] = $value["total_fee"];
        $data["body"] = $value["body"];
        $data["subject"] = $value["subject"];
        $data["currency"] = $value["currency"];
        $data["product_code"] = $value["product_code"];
        $data["extend_params"] = $value["extend_params"];
        $data["goods_detail"] = $value["goods_detail"];
        $data["risk_info"] = $value["risk_info"];
        $data["it_b_pay"] = $value["it_b_pay"];
        $data["passback_parameters"] = $value["passback_parameters"];
        $data["notify_url"] = $value["notify_url"];

        //$data["return_url"] = $value["return_url"];
        //$data["sub_appid"] = $value["sub_appid"];

        return $this->sign($data);
    }

    /**
     * 測試，返回簽名
     */
    public  function sign_test($value)
    {
        return $this->sign($value);
    }
}
