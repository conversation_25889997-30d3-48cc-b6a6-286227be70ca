﻿各文件功能如下說明, 接收開發SDK後請根據開發需求查閱對應文件
The functions of each file are described as follows. After receiving the development SDK, refer to the corresponding file based on development requirements


1.)澳門通商戶統一接入接口規範v2.0.1------->開發接口文檔,當中包含接口資訊及基本配置地址
1.) MACAU Pass API Specification v2.0.1-------> Develop API document, which contains API information and gateway address

2.)澳門通OPENAPI接入商戶資料 ------------------->測試環境用的商戶資料,當中包含測試商戶號、交易密鑰及商戶參數。
2.) MACAU Pass UAT test merchant information ---> UAT merchant data, including a merchants ID, key and merchants parameters.

3.)OpenAPI 接入驗收大綱 --------------------->完成功能接入後需填寫本驗收報告並提供測試資源,測試環境驗收完成後將安排開通正式環境商戶號(*驗收流程需5-7個工作日)
3.)OpenAPI acceptance doc. --------> This acceptance report shall be filled in and test resources shall be provided after function access is completed.
After the test environment acceptance is completed, an official environmental merchant ID will be provide. (* Acceptance process takes 5-7 working days).


4.)OPENAPI 接入Q&A --------------->接入常見問題集,簡單的功能問題可先查閱問題集內容
4) OPENAPI Q&A ------------> access common problem, the function of simple problem can consult the first set of content


5.)OpenAPI_Receipt_standard_POSPrint_v1.0------>實體收據打印格式規範,適用於線下接入並需打印紙質收據的場景
5.)OpenAPI_Receipt_standard_POsprint_v1.0 ------> Print format of receipts, applicable to offline scenarios where receipts need to be printed


6.)MPay Branding Visual Identity_V1.0.1-------->線上支付樣式規範,適用於線上(APP/PC的場景)文字內容及支付Logo需按檔案內格式配置
6.)MPay Branding Visual identity_v1.0.1 --------> Online payment specification, suitable for online (APP/PC scenario) text content and payment Logo should be configured according to file format


6.1)MP在線支付logo素材------------------------->配合線上支付樣式規範使用
6.1) MP online payment logo material ----------> used with online payment style specification


7.)OpenAPI Demo(Java)-------------------------->Java版本Demo資源

7.)OpenAPI Demo (Java)------------------------> Java version Demo resources


7.1)MPay Demo(PHP)----------------------------->PHP版本Demo資源
7.1) MPay Demo (PHP) --------------------------> PHP version Demo resources


7.2)MPay APP支付OpenSDK(IOS)-------------->APP Demo資源,內含IOS版本
7.2) pay OpenSDK MPay APP (IOS)-----------> Demo APP resources, it contains the IOS version

7.3)MPay APP支付Android SDK--------------->APP Demo資源,內含安卓版本
7.3) MPay APP paid Android SDK------------> Demo APP resources, containing the Android version
































